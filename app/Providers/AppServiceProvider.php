<?php

namespace App\Providers;

use App\Models\Visitor;
use Illuminate\Support\ServiceProvider;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rules\Password;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;
use Sentry\State\HubInterface;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(HubInterface $hub)
    {

        Model::unguard();

        Schema::defaultStringLength(191);

        JsonResource::withoutWrapping();

        Password::defaults(function () {
            return Password::min(8)
                ->letters()
                ->numbers()
                ->symbols()
                ->uncompromised();
        });

        //if ($this->app->isLocal()) {
        RateLimiter::for('throttle-key', function () {
            return Limit::none();
        });
        //}

        try {
            if ($user = getUserCached()) {
                $hub->configureScope(function (\Sentry\State\Scope $scope) use ($user) {
                    if ($user) {
                        if ($user instanceof Visitor) {
                            $userData = [
                                'id' => $user->visitorId,
                                'visitorId' => $user->visitorId,
                                'visitorKey' => $user->visitorKey,
                                'type' => 'visitor'
                            ];
                        } else {
                            $userData = [
                                'id' => $user->userId,
                                'userId' => $user->userId,
                                'firstName' => $user->firstName ?? null,
                                'lastName' => $user->lastName ?? null,
                                'phone' => $user->phone ?? null,
                                'email' => $user->email ?? null,
                                'type' => 'user'
                            ];
                        }

                        $scope->setUser($userData);
                    }
                });
            }
        } catch (\Exception $e) {
            // If Sentry user context setting fails, log but don't break the application
            \Log::warning('Failed to set Sentry user context in AppServiceProvider: ' . $e->getMessage());
        }
    }
}