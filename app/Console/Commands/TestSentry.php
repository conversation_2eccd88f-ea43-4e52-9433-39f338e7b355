<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Sen<PERSON>\Laravel\Facade as Sentry;

class TestSentry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sentry:test {--type=info : Type of test (info, warning, error, exception)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Sentry integration by sending test events';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->option('type');
        
        $this->info('Testing Sentry integration...');
        
        // Check if Sentry DSN is configured
        $dsn = config('sentry.dsn');
        if (empty($dsn)) {
            $this->error('Sentry DSN is not configured!');
            return 1;
        }
        
        $this->info("Sentry DSN: " . substr($dsn, 0, 50) . '...');
        
        try {
            switch ($type) {
                case 'info':
                    Sentry::addBreadcrumb('Test breadcrumb from console command', 'info');
                    Sentry::captureMessage('Test info message from PHP-BS backend', 'info');
                    $this->info('✅ Info message sent to Sentry');
                    break;
                    
                case 'warning':
                    Sentry::captureMessage('Test warning message from PHP-BS backend', 'warning');
                    $this->info('✅ Warning message sent to Sentry');
                    break;
                    
                case 'error':
                    Sentry::captureMessage('Test error message from PHP-BS backend', 'error');
                    $this->info('✅ Error message sent to Sentry');
                    break;
                    
                case 'exception':
                    try {
                        throw new \Exception('Test exception from PHP-BS backend console command');
                    } catch (\Exception $e) {
                        Sentry::captureException($e);
                        $this->info('✅ Exception sent to Sentry');
                    }
                    break;
                    
                default:
                    $this->error('Invalid test type. Use: info, warning, error, or exception');
                    return 1;
            }
            
            $this->info('✅ Sentry test completed successfully!');
            $this->info('Check your Sentry dashboard to see the test event.');
            
        } catch (\Exception $e) {
            $this->error('❌ Sentry test failed: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
